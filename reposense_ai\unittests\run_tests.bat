@echo off
REM RepoSense AI Test Runner for Windows
REM This batch file provides easy test execution options
REM Run from the unittests directory

echo.
echo ========================================
echo   RepoSense AI Test Runner
echo ========================================
echo.

if "%1"=="--help" goto :help
if "%1"=="-h" goto :help
if "%1"=="/?" goto :help

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python and ensure it's in your PATH
    pause
    exit /b 1
)

REM Check if pytest is available
python -m pytest --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: pytest is not installed
    echo Installing pytest and testing dependencies...
    python -m pip install pytest pytest-cov pytest-mock pytest-timeout pytest-xdist
    if errorlevel 1 (
        echo ERROR: Failed to install pytest
        pause
        exit /b 1
    )
)

REM Change to parent directory (reposense_ai) for proper imports
cd ..

REM Default: run all tests
if "%1"=="" (
    echo Running all tests...
    python -m pytest unittests/ -v
    goto :end
)

REM Handle specific options
if "%1"=="--basic" (
    echo Running basic functionality test...
    python -m pytest unittests/test_config_manager.py::test_basic_functionality -v
    goto :end
)

if "%1"=="--unit" (
    echo Running unit tests only...
    python -m pytest unittests/ -m unit -v
    goto :end
)

if "%1"=="--coverage" (
    echo Running tests with coverage report...
    python -m pytest unittests/ --cov=. --cov-report=html --cov-report=term-missing -v
    echo.
    echo Coverage report generated in htmlcov/index.html
    goto :end
)

if "%1"=="--config" (
    echo Running ConfigManager tests...
    python -m pytest unittests/test_config_manager.py -v
    goto :end
)

if "%1"=="--change-requests" (
    echo Running Change Request Integration tests...
    python -m pytest unittests/test_change_request_integration.py -v
    goto :end
)

if "%1"=="--database" (
    echo Running Database Connection tests...
    python -m pytest unittests/test_database_connection.py -v
    goto :end
)

if "%1"=="--pipeline" (
    echo Running Complete Pipeline Integration tests...
    python -m pytest unittests/test_complete_pipeline_integration.py -v
    goto :end
)

if "%1"=="--document-fixes" (
    echo Running Document Fixes tests...
    python -m pytest unittests/test_document_fixes.py -v
    goto :end
)

if "%1"=="--integration" (
    echo Running all integration tests...
    python -m pytest unittests/test_change_request_integration.py unittests/test_complete_pipeline_integration.py unittests/test_document_fixes.py -v
    goto :end
)

REM If we get here, unknown option was provided
echo Unknown option: %1
echo Use --help for available options
goto :end

:help
echo.
echo Usage: run_tests.bat [option]
echo.
echo Options:
echo   (no option)         Run all tests
echo   --basic             Run basic functionality test only
echo   --unit              Run unit tests only
echo   --coverage          Run tests with coverage report
echo   --config            Run ConfigManager tests only
echo   --change-requests   Run Change Request Integration tests
echo   --pipeline          Run Complete Pipeline Integration tests
echo   --document-fixes    Run Document Fixes tests
echo   --integration       Run all integration tests
echo   --help, -h          Show this help message
echo.
echo Examples:
echo   run_tests.bat
echo   run_tests.bat --basic
echo   run_tests.bat --coverage
echo.
echo Note: This script should be run from the unittests directory
echo       It will automatically change to the parent directory for proper imports
echo.
goto :end

:end
echo.
pause
