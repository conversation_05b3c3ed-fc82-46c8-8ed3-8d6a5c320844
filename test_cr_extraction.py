#!/usr/bin/env python3
"""Test change request extraction with real commit data"""

import sys
sys.path.append('/app')
from config_manager import ConfigManager
from change_request_service import ChangeRequestService
import sqlite3
import re

def main():
    # Load config
    cm = ConfigManager('/app/data/config.json')
    config = cm.load_config()
    service = ChangeRequestService(config)

    # Connect to the local SQLite database to get commit messages
    db_path = '/app/data/reposense_ai.db'
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    print('Fetching top 300 commit records from source control database...')
    print()

    # Get the latest 300 commits with their messages
    cursor.execute('''
        SELECT revision, author, message, repository_name 
        FROM commits 
        ORDER BY date DESC 
        LIMIT 300
    ''')

    commits = cursor.fetchall()
    print(f'Retrieved {len(commits)} commit records')
    print()

    # Test change request extraction on these commits
    extraction_results = []
    for revision, author, message, repo in commits:
        if message:
            # Extract change request numbers using our service
            cr_numbers = service.extract_change_request_numbers(message)
            if cr_numbers:
                extraction_results.append({
                    'revision': revision,
                    'author': author,
                    'message': message[:100] + '...' if len(message) > 100 else message,
                    'repository': repo,
                    'extracted_numbers': cr_numbers
                })

    print(f'Found {len(extraction_results)} commits with potential change request numbers:')
    print('=' * 80)

    for i, result in enumerate(extraction_results[:20], 1):  # Show first 20
        print(f'{i}. Revision {result["revision"]} by {result["author"]}')
        print(f'   Repository: {result["repository"]}')
        print(f'   Message: {result["message"]}')
        print(f'   Extracted: {result["extracted_numbers"]}')
        print()

    if len(extraction_results) > 20:
        print(f'... and {len(extraction_results) - 20} more')

    conn.close()

    # Now test if these extracted numbers exist in the change request database
    print('\n' + '=' * 80)
    print('Testing extracted numbers against change request database...')
    print('=' * 80)

    valid_crs = []
    invalid_crs = []

    for result in extraction_results:
        for cr_number in result['extracted_numbers']:
            try:
                cr_info = service.get_change_request_info(cr_number)
                if cr_info:
                    valid_crs.append({
                        'number': cr_number,
                        'title': cr_info.title,
                        'status': cr_info.status,
                        'priority': cr_info.priority,
                        'commit_revision': result['revision'],
                        'commit_message': result['message']
                    })
                    print(f'✅ CR {cr_number}: {cr_info.title} (Status: {cr_info.status})')
                else:
                    invalid_crs.append({
                        'number': cr_number,
                        'commit_revision': result['revision'],
                        'commit_message': result['message']
                    })
                    print(f'❌ CR {cr_number}: Not found in database')
            except Exception as e:
                invalid_crs.append({
                    'number': cr_number,
                    'commit_revision': result['revision'],
                    'commit_message': result['message'],
                    'error': str(e)
                })
                print(f'❌ CR {cr_number}: Error - {e}')

    print(f'\nSummary:')
    print(f'Valid change requests found: {len(valid_crs)}')
    print(f'Invalid/missing change requests: {len(invalid_crs)}')
    
    if valid_crs:
        accuracy = len(valid_crs) / (len(valid_crs) + len(invalid_crs)) * 100
        print(f'Accuracy: {accuracy:.1f}%')

if __name__ == '__main__':
    main()
