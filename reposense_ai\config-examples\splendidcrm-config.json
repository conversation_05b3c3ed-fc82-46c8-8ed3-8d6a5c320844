{"sql_config": {"enabled": true, "host": "splendiddb.public.d490bb5f4eae.database.windows.net,3342", "port": 1433, "database": "SplendidCRM_TriMicro", "username": "tmwGPT", "password": "ZaQXa$TP6J", "driver": "mssql", "connection_timeout": 30, "query_timeout": 60, "change_request_query": "SELECT [BUG_NUMBER] as id, [BUG_NUMBER] as number, [NAME] as title, [DESCRIPTION] as description, [PRIORITY] as priority, [STATUS] as status, [DATES] as created_date, [ASSIGNED_TO_NAME] as assigned_to, [TYPE] as category, [PRIORITY] as risk_level FROM [SplendidCRM_TriMicro].[dbo].[vwBUGS] WHERE [BUG_NUMBER] = :change_request_number", "change_request_patterns": ["BUG[#\\-\\s]*(\\d+)", "Bug[#\\-\\s]*(\\d+)", "bug[#\\-\\s]*(\\d+)", "CR[#\\-\\s]*(\\d+)", "Change[#\\-\\s]*(\\d+)", "Request[#\\-\\s]*(\\d+)", "Ticket[#\\-\\s]*(\\d+)", "Issue[#\\-\\s]*(\\d+)", "#(\\d+)", "SPLENDID[#\\-\\s]*(\\d+)", "CRM[#\\-\\s]*(\\d+)"]}}