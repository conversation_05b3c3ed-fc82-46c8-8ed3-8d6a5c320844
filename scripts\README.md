# RepoSense AI Build & Deployment Scripts

This directory contains scripts for building, deploying, and managing RepoSense AI in various environments.

## 🚀 Deployment Scripts

### `build-for-deployment.sh` / `.bat` / `.ps1`

**Purpose**: Build RepoSense AI for production deployment
**Platforms**: Linux/macOS (`.sh`), Windows (`.bat`, `.ps1`)
**Usage**:

```bash
# Linux/macOS
./scripts/build-for-deployment.sh

# Windows (Command Prompt)
scripts\build-for-deployment.bat

# Windows (PowerShell)
scripts\build-for-deployment.ps1
```

**What it does**:

- Creates production-ready Docker images
- Optimizes image size and performance
- Validates build integrity
- Prepares deployment artifacts

## 🔧 Setup & Integration Scripts

### `setup-integration.sh`

**Purpose**: Set up RepoSense AI for integration with existing Docker Compose environments
**Usage**: `./scripts/setup-integration.sh`
**Features**:

- Configures network integration
- Sets up volume mounts
- Validates service dependencies
- Creates integration-ready configuration

### `migrate-to-subdirectory.sh`

**Purpose**: Migrate existing RepoSense AI installation to subdirectory structure
**Usage**: `./scripts/migrate-to-subdirectory.sh`
**Use case**: Upgrading from older versions with different directory structure

## 🔍 Validation & Maintenance Scripts

### `validate-structure.sh`

**Purpose**: Validate project structure and configuration
**Usage**: `./scripts/validate-structure.sh`
**Checks**:

- Directory structure integrity
- Configuration file validity
- Docker Compose syntax
- Required dependencies

### `fix-docker-permissions.sh` / `.ps1`

**Purpose**: Fix Docker volume permissions on the HOST side (before container starts)
**Platforms**: Linux/macOS (`.sh`), Windows (`.ps1`)
**Usage**:

```bash
# Linux/macOS
./scripts/fix-docker-permissions.sh

# Windows (PowerShell)
.\scripts\fix-docker-permissions.ps1
```

**Use case**: Resolving host-side permission issues before starting containers

### `fix-container-permissions.sh` / `.ps1`

**Purpose**: Fix permissions INSIDE a running Docker container
**Platforms**: Linux/macOS (`.sh`), Windows (`.ps1`)
**Usage**:

```bash
# Linux/macOS
./scripts/fix-container-permissions.sh

# Windows (PowerShell)
.\scripts\fix-container-permissions.ps1
```

**Use case**: Resolving permission issues within a running container

### 🔧 Permission Scripts Usage Guide

**When to use `fix-docker-permissions.sh/.ps1`** (Host-side):

- Before starting RepoSense AI for the first time
- After copying files to the data directory
- When getting "permission denied" errors during container startup
- When the container can't write to mounted volumes

**When to use `fix-container-permissions.sh/.ps1`** (Container-side):

- When the container is running but can't access its own files
- After manually copying files into a running container
- When getting permission errors from within the application
- When config.json or database files have wrong ownership

**Quick Decision Guide**:

```
Container not starting? → Use fix-docker-permissions
Container running but app has errors? → Use fix-container-permissions
```

## 📋 Usage Guidelines

### Prerequisites

- Docker and Docker Compose installed
- Bash shell (for `.sh` scripts)
- Appropriate permissions for script execution

### Making Scripts Executable

```bash
# Linux/macOS
chmod +x scripts/*.sh

# Windows - scripts are already executable
```

### Environment Variables

Some scripts may require environment variables:

- `DOCKER_REGISTRY`: Custom Docker registry (optional)
- `BUILD_VERSION`: Version tag for builds (optional)
- `DEPLOYMENT_ENV`: Target environment (dev/staging/prod)

### Script Dependencies

- **Docker**: All scripts require Docker to be installed and running
- **Docker Compose**: Required for multi-service deployments
- **Git**: Some scripts may require Git for version information
- **Network Access**: Build scripts may need internet access for dependencies

## 🔧 Customization

### Modifying Build Scripts

To customize the build process:

1. Copy the appropriate script to a new name
2. Modify build parameters as needed
3. Update Docker image tags and registry settings
4. Test thoroughly before production use

### Adding New Scripts

When adding new scripts:

1. Follow the existing naming convention
2. Include appropriate error handling
3. Add usage documentation
4. Test on target platforms
5. Update this README

## 🚨 Troubleshooting

### Common Issues

**Permission Denied**:

```bash
chmod +x scripts/script-name.sh
```

**Docker Not Running**:

```bash
# Start Docker service
sudo systemctl start docker  # Linux
# or start Docker Desktop    # Windows/macOS
```

**Build Failures**:

- Check Docker daemon status
- Verify network connectivity
- Review build logs for specific errors
- Ensure sufficient disk space

**Integration Issues**:

- Validate Docker Compose syntax
- Check network configuration
- Verify volume mount paths
- Review service dependencies

### Getting Help

- Check script output for error messages
- Review Docker logs: `docker-compose logs`
- Validate configuration files
- Consult main documentation in `/docs`

## 📝 Script Maintenance

### Regular Updates

- Keep scripts synchronized with application changes
- Update Docker base images and dependencies
- Review and update documentation
- Test scripts with new Docker/Compose versions

### Version Compatibility

- Scripts are designed for Docker Compose V2
- Compatible with Docker 20.10+
- Tested on Linux, macOS, and Windows
- May require updates for major version changes

## 🔄 Integration Examples

### CI/CD Pipeline Integration

```yaml
# Example GitHub Actions workflow
- name: Build for deployment
  run: ./scripts/build-for-deployment.sh
  
- name: Validate structure
  run: ./scripts/validate-structure.sh
```

### Production Deployment

```bash
# Complete deployment workflow
./scripts/build-for-deployment.sh
./scripts/validate-structure.sh
docker-compose -f docker-compose.prod.yml up -d
```

These scripts are designed to simplify RepoSense AI deployment and maintenance across different environments and platforms.
