# 🤖 RepoSense AI

**Intelligent Repository Monitoring & AI-Powered Code Analysis**

RepoSense AI transforms repository monitoring with advanced AI-powered insights, automated documentation generation, and comprehensive change analysis. Monitor multiple repositories, assess risk levels, and generate intelligent summaries of code changes with local AI processing.

**🆕 Latest Enhancements:**

- **🔔 Advanced Notification System** - Comprehensive user-repository relationships with granular notification preferences
- **🗄️ Consolidated Database Architecture** - Unified SQLite database for improved performance and reliability
- **🔗 Enhanced SQL Server Integration** - Microsoft ODBC Driver 18 with Azure SQL Database and SplendidCRM support
- **👥 Enhanced User Management** - Role-based access control with repository-specific notification settings
- **🧪 Production-Ready Testing** - 97.7% unit test coverage with comprehensive test framework

---

## 🚀 Quick Start

### **One-Command Setup**

```bash
# Clone and start all services
git clone <repository-url>
cd reposense_ai
docker-compose up -d
```

### **Access Points**

- **🌐 RepoSense AI**: <http://localhost:5001>
- **📧 MailHog (Email Testing)**: <http://localhost:8025>
- **🤖 Ollama (AI Service)**: <http://localhost:11434>

### **First-Time Configuration**

1. **Configure AI Models** → Configuration → AI Settings → Select Models
2. **Add Repository** → Repository Discovery → Enter URL → Configure Monitoring  
3. **Test Email** → Configuration → Email Settings → "Test Send Email" Button

---

## 📚 Complete Documentation

| Guide | Purpose | Link |
|-------|---------|------|
| **🚀 Quick Start** | Get running in 5 minutes | [quick-start.md](reposense_ai/docs/quick-start.md) |
| **⚙️ Configuration** | Detailed setup options | [configuration.md](reposense_ai/docs/configuration.md) |
| **✨ Features** | Complete feature overview | [features.md](reposense_ai/docs/features.md) |
| **🛠️ Development** | Development setup guide | [development.md](reposense_ai/docs/development.md) |
| **📋 Changelog** | Version history & updates | [CHANGELOG.md](reposense_ai/docs/CHANGELOG.md) |

---

## 🏗️ Project Structure

```
reposense_ai/
├── 📁 reposense_ai/           # Main application
│   ├── 🔧 tools/             # Admin & maintenance utilities  
│   ├── 🧪 tests/             # Comprehensive test suite
│   ├── 🛠️ utils/             # Shared utility modules
│   ├── 📚 docs/              # Documentation
│   ├── 🌐 templates/         # Web interface templates
│   ├── 📊 static/            # Web assets (CSS, JS)
│   └── 🐍 *.py               # Core application files
├── 📁 scripts/               # Build & deployment scripts
├── 📁 docs/                  # Root-level documentation
├── 🐳 docker-compose.yml     # Multi-service configuration
└── 📖 README.md             # This file
```

---

## ✨ Key Features

### 🤖 **AI-Powered Analysis**

- **Local AI Processing** via Ollama (no external dependencies)
- **Risk Assessment** with configurable aggressiveness levels
- **Intelligent Summaries** of code changes and impact
- **Multiple AI Models** (Qwen2.5-Coder, CodeLlama, Llama3+)

### 📊 **Repository Monitoring**

- **Multi-Repository Support** (Git & SVN)
- **Real-Time Processing** of new commits
- **Branch Monitoring** (specific branches or all)
- **Flexible Scheduling** with configurable intervals

### 📄 **Advanced Document Processing**

- **Office Documents** (Word, Excel, PowerPoint)
- **PDF Analysis** with text extraction
- **Structured Diffs** showing actual content changes
- **Binary File Intelligence** (no more "binary file changed")

### 📧 **Advanced Notification System**

- **Comprehensive Notifications** for commits, system health, and security alerts
- **User-Repository Relationships** with role-based notification preferences
- **Test Email Button** for instant validation and configuration testing
- **MailHog Integration** for development testing and email capture
- **Production SMTP** (Gmail, Outlook, custom) with multi-provider support
- **Granular Preferences** with category-specific and severity-based filtering
- **Digest & Immediate** delivery options with customizable timing

### 🌐 **Web Interface**

- **Intuitive Dashboard** with real-time updates
- **Configuration Management** with validation
- **Responsive Design** (desktop & mobile)
- **Progress Tracking** with detailed status
- **User Notification Management** with granular preference controls

### 🗄️ **Database Architecture**

- **Consolidated SQLite Database** for improved performance and data consistency
- **Automatic Migration System** with schema versioning and backward compatibility
- **User-Repository Relationships** with role-based access control and notification preferences
- **Comprehensive Data Integrity** with foreign key constraints and validation
- **Efficient Indexing** for fast queries and optimal performance

### 🧪 **Production-Ready Testing**

- **97.7% Unit Test Coverage** with comprehensive test framework
- **347 Total Tests** covering all major components and edge cases
- **Professional Testing Infrastructure** with pytest, mocking, and fixtures
- **Continuous Integration Ready** with automated test execution and reporting

---

## 🔧 Development & Testing

### **Run Tests**

```bash
# Comprehensive Unit Test Suite (97.7% coverage)
docker exec reposense-ai sh -c "cd /app/unittests && python -m pytest -v"

# Quick Test Summary
docker exec reposense-ai sh -c "cd /app/unittests && python -m pytest --tb=line -q"

# Specific Component Tests
docker exec reposense-ai sh -c "cd /app/unittests && python -m pytest test_notification_system.py -v"
docker exec reposense-ai sh -c "cd /app/unittests && python -m pytest test_document_service.py -v"
docker exec reposense-ai sh -c "cd /app/unittests && python -m pytest test_email_service.py -v"

# Legacy Integration Tests
python reposense_ai/tests/test_ollama_connection.py
python reposense_ai/tests/test_svn_connection.py
python reposense_ai/tests/test_mailhog_setup.py
```

### **Administrative Tools**

```bash
# Database Maintenance
python reposense_ai/tools/check_database_schema.py
python reposense_ai/tools/fix_document_id.py

# System Monitoring
python reposense_ai/tools/check_processor_status.py
python reposense_ai/tools/check_document_status.py

# Configuration Management
python reposense_ai/tools/configure_email_test.py
python reposense_ai/tools/update_risk_aggressiveness.py
```

---

## 🚀 Production Deployment

### **Docker Compose Configuration**

```yaml
version: '3.8'
services:
  reposense-ai:
    build: ./reposense_ai
    ports:
      - "5001:5001"
    volumes:
      - ./reposense_ai/data:/app/data
      - ./reposense_ai/logs:/app/logs
    environment:
      - OLLAMA_BASE_URL=http://ollama:11434
    depends_on:
      - ollama

  ollama:
    image: ollama/ollama:latest
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

volumes:
  ollama_data:
```

### **Build & Deployment Scripts**

```bash
# Build for deployment
./scripts/build-for-deployment.sh

# Setup integration
./scripts/setup-integration.sh

# Validate structure
./scripts/validate-structure.sh

# Fix permission issues
./scripts/fix-docker-permissions.sh      # Host-side permissions
./scripts/fix-container-permissions.sh   # Container-side permissions
```

---

## 🔒 Security & Privacy

- **🏠 Local Processing**: All AI analysis runs locally via Ollama
- **🔐 No External Dependencies**: No data sent to external AI services  
- **👥 User Access Control**: Configurable user-based permissions
- **💾 Secure Storage**: Local SQLite database with retention policies
- **🔍 Audit Trail**: Complete logging of all analysis activities

---

## 🤝 Contributing

1. **Fork** the repository
2. **Create** a feature branch (`git checkout -b feature/amazing-feature`)
3. **Make** your changes with tests
4. **Update** documentation
5. **Submit** a pull request

### **Development Guidelines**

- Add tests for new functionality
- Update relevant documentation
- Follow existing code style
- Test with multiple AI models
- Validate email functionality

---

## 📊 System Requirements

### **Minimum Requirements**

- **CPU**: 4 cores (8+ recommended for AI processing)
- **RAM**: 8GB (16GB+ recommended with multiple models)
- **Storage**: 10GB (additional space for AI models)
- **Docker**: Version 20.10+ with Compose V2

### **Recommended Setup**

- **GPU**: NVIDIA GPU for faster AI processing (optional)
- **SSD**: Fast storage for database and model loading
- **Network**: Stable connection for repository access
- **Backup**: Regular backup solution for data persistence

---

## 🆘 Support & Troubleshooting

### **Common Issues**

- **AI Connection**: Check Ollama service status and model availability
- **Email Testing**: Use "Test Send Email" button for instant validation
- **Repository Access**: Verify credentials and network connectivity
- **Performance**: Monitor resource usage and adjust model settings

### **Getting Help**

- **📚 Documentation**: Comprehensive guides in `/docs` directory
- **🧪 Test Suite**: Run tests to validate system functionality  
- **🔧 Admin Tools**: Use built-in tools for system diagnostics
- **📝 Logs**: Check application logs in `/logs` directory

---

## 📈 Latest Updates

**Enhanced Email Testing & Document Processing** - Latest release includes:

- **One-Click Email Validation** with comprehensive test emails
- **Advanced Document Support** for Office files and PDFs
- **Intelligent Error Analysis** with specific troubleshooting guidance
- **Organized Codebase** with professional directory structure

For complete version history, see [Changelog](reposense_ai/docs/CHANGELOG.md).

---

**🎯 Ready to transform your repository monitoring with AI-powered insights?**

**Get started in minutes:** `docker-compose up -d` → <http://localhost:5001>
