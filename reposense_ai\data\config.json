{"server": {"id": "3c7da7f8-b206-48b5-b6af-3f2e1a1ff6e6", "name": "TMW", "description": "", "base_url": "https://lserver2.TMW.local/svn", "default_username": "devuser", "default_password": "chilly$weather", "enabled": true}, "teams": [], "ollama_host": "http://*************:11434", "ollama_model": "qwen3-coder:latest", "ollama_model_documentation": null, "ollama_model_code_review": null, "ollama_model_risk_assessment": null, "ollama_timeout_base": 300, "ollama_timeout_connection": 30, "ollama_timeout_embeddings": 60, "use_enhanced_prompts": true, "enhanced_prompts_fallback": true, "check_interval": 300, "cleanup_orphaned_documents": false, "svn_server_url": "https://lserver2.TMW.local/svn", "svn_server_username": "devuser", "svn_server_password": "chilly$weather", "svn_server_type": "auto", "smtp_host": "mailhog", "smtp_port": 1025, "smtp_username": null, "smtp_password": null, "email_from": "reposense-ai@localhost", "email_recipients": [], "output_dir": "/app/data/output", "generate_docs": true, "send_emails": false, "web_enabled": true, "web_port": 5000, "web_host": "0.0.0.0", "web_secret_key": "8da3247b97482c0943d4ba75f3278262631313a8b86b78abba026bc8b1233a50", "web_log_entries": 300, "log_cleanup_max_size_mb": 50, "log_cleanup_lines_to_keep": 1000, "log_rotation_max_size_mb": 10, "log_rotation_backup_count": 5, "ldap_sync_enabled": false, "ldap_server": "", "ldap_port": 389, "ldap_use_ssl": false, "ldap_bind_dn": "", "ldap_bind_password": "", "ldap_user_base_dn": "ou=users,dc=company,dc=com", "ldap_user_filter": "(objectClass=person)", "ldap_sync_interval": 3600, "ldap_username_attr": "sAMAccountName", "ldap_email_attr": "mail", "ldap_fullname_attr": "displayName", "ldap_phone_attr": "telephoneNumber", "ldap_groups_attr": "memberOf", "ldap_group_role_mapping": {"CN=RepoSense-Admins,OU=Groups,DC=company,DC=com": "ADMIN", "CN=RepoSense-Managers,OU=Groups,DC=company,DC=com": "MANAGER", "CN=Developers,OU=Groups,DC=company,DC=com": "DEVELOPER", "CN=RepoSense-Viewers,OU=Groups,DC=company,DC=com": "VIEWER"}, "ldap_default_role": "VIEWER", "sql_config": {"enabled": true, "host": "splendiddb.public.d490bb5f4eae.database.windows.net,3342", "port": 1433, "database": "SplendidCRM_TriMicro", "username": "tmwGPT", "password": "ZaQXa$TP6J", "driver": "mssql", "connection_timeout": 30, "query_timeout": 60, "change_request_query": "SELECT [BUG_NUMBER] as id, [BUG_NUMBER] as number, [NAME] as title, [DESCRIPTION] as description, [PRIORITY] as priority, [STATUS] as status, [DATES] as created_date, [ASSIGNED_TO_NAME] as assigned_to, [TYPE] as category, [PRIORITY] as risk_level FROM [SplendidCRM_TriMicro].[dbo].[vwBUGS] WHERE [BUG_NUMBER] = :change_request_number", "change_request_patterns": ["BUG[#\\-]\\s*(\\d+)", "BUG\\s+(\\d+):", "Bug[#\\-]\\s*(\\d+)", "Bug\\s+(\\d+):", "bug[#\\-]\\s*(\\d+)", "bug\\s+(\\d+):", "CR[#\\-]\\s*(\\d+)", "CR\\s+(\\d+):", "CR\\s+(\\d+)", "Change[#\\-]\\s*(\\d+)", "Request[#\\-]\\s*(\\d+)", "Ticket[#\\-]\\s*(\\d+)", "Ticket\\s+(\\d+):", "Issue[#\\-]\\s*(\\d+)", "Issue\\s+(\\d+):", "issue[#\\-]\\s*(\\d+)", "issue\\s+(\\d+):", "#(\\d+)"], "field_mappings": {"risk_level_field": "risk_level", "priority_field": "priority", "category_field": "category", "status_field": "status"}, "value_mappings": {"priority_to_risk": {"CRITICAL": "CRITICAL", "URGENT": "CRITICAL", "HIGH": "HIGH", "IMPORTANT": "HIGH", "MEDIUM": "MEDIUM", "NORMAL": "MEDIUM", "LOW": "LOW", "MINOR": "LOW"}, "category_risk_boost": {"SECURITY": "CRITICAL", "CRITICAL_BUG": "CRITICAL", "DATA_LOSS": "CRITICAL", "PERFORMANCE": "HIGH", "INTEGRATION": "HIGH", "API_CHANGE": "HIGH"}}, "risk_analysis_config": {"supported_risk_levels": ["CRITICAL", "HIGH", "MEDIUM", "LOW"], "default_risk_level": "MEDIUM", "confidence_threshold": 0.3, "base_confidence": 0.6, "max_confidence": 0.9, "category_weight": 0.3}, "summary_display_config": {"enabled": true, "section_title": "Change Request Summary", "display_fields": ["number", "title", "priority", "status", "risk_level", "category", "assigned_to", "created_date", "description"], "field_labels": {"number": "Request Number", "title": "Title", "priority": "Priority", "status": "Status", "risk_level": "Risk Level", "category": "Category", "assigned_to": "Assigned To", "created_date": "Created Date", "description": "Description"}, "description_max_length": 10000, "date_format": "%Y-%m-%d", "show_empty_fields": false}}}