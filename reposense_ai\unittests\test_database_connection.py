#!/usr/bin/env python3
"""
Unit tests for Database Connection functionality
Incorporates tests from test-data/test-connection.py into the unit test framework
"""

import os
import re
import sqlite3
import tempfile
import unittest
from typing import List, Optional
from unittest.mock import Mock, patch

import pytest

# Import the modules we're testing
try:
    from change_request_service import ChangeRequestService
    from models import ChangeRequestInfo, Config, SqlConfig
    CHANGE_REQUEST_AVAILABLE = True
except ImportError as e:
    print(f"Change request modules not available: {e}")
    CHANGE_REQUEST_AVAILABLE = False

# Try to import MySQL driver for integration tests
try:
    import pymysql
    MYSQL_AVAILABLE = True
except ImportError:
    MYSQL_AVAILABLE = False


class TestDatabaseConnection(unittest.TestCase):
    """Test database connection functionality"""

    def setUp(self):
        """Set up test fixtures"""
        if not CHANGE_REQUEST_AVAILABLE:
            self.skipTest("Change request modules not available")

        self.test_db_path = None
        self.change_request_service = None

    def tearDown(self):
        """Clean up test fixtures"""
        if self.test_db_path and os.path.exists(self.test_db_path):
            os.unlink(self.test_db_path)

    def create_test_sqlite_database(self) -> str:
        """Create a temporary SQLite test database with sample change request data"""
        # Create temporary database
        fd, self.test_db_path = tempfile.mkstemp(suffix="_connection_test.db")
        os.close(fd)

        conn = sqlite3.connect(self.test_db_path)
        cursor = conn.cursor()

        # Create change requests table (matching MySQL schema)
        cursor.execute("""
            CREATE TABLE change_requests (
                id INTEGER PRIMARY KEY,
                number VARCHAR(50) UNIQUE,
                title TEXT,
                description TEXT,
                status VARCHAR(20),
                priority VARCHAR(20),
                category VARCHAR(50),
                assigned_to VARCHAR(100),
                created_date DATETIME,
                updated_date DATETIME,
                risk_level VARCHAR(20)
            )
        """)

        # Insert test change requests (from original test-connection.py)
        test_change_requests = [
            {
                "number": "123",
                "title": "Fix authentication bug",
                "description": "Resolve authentication issues in login system",
                "status": "OPEN",
                "priority": "HIGH",
                "category": "BUG",
                "assigned_to": "<EMAIL>",
                "created_date": "2024-01-15 10:00:00",
                "updated_date": "2024-01-16 15:30:00",
                "risk_level": "MEDIUM",
            },
            {
                "number": "124",
                "title": "Implement reporting feature",
                "description": "Add comprehensive reporting functionality",
                "status": "IN_PROGRESS",
                "priority": "MEDIUM",
                "category": "FEATURE",
                "assigned_to": "<EMAIL>",
                "created_date": "2024-01-10 09:00:00",
                "updated_date": "2024-01-20 11:00:00",
                "risk_level": "LOW",
            },
            {
                "number": "125",
                "title": "Database optimization",
                "description": "Optimize database queries for better performance",
                "status": "RESOLVED",
                "priority": "HIGH",
                "category": "PERFORMANCE",
                "assigned_to": "<EMAIL>",
                "created_date": "2024-01-05 14:00:00",
                "updated_date": "2024-01-25 16:00:00",
                "risk_level": "HIGH",
            },
            {
                "number": "127",
                "title": "Security patch deployment",
                "description": "Deploy critical security patches",
                "status": "CLOSED",
                "priority": "CRITICAL",
                "category": "SECURITY",
                "assigned_to": "<EMAIL>",
                "created_date": "2024-01-01 08:00:00",
                "updated_date": "2024-01-30 17:00:00",
                "risk_level": "CRITICAL",
            },
            {
                "number": "128",
                "title": "UI improvements",
                "description": "Enhance user interface for better usability",
                "status": "OPEN",
                "priority": "LOW",
                "category": "ENHANCEMENT",
                "assigned_to": "<EMAIL>",
                "created_date": "2024-01-12 13:00:00",
                "updated_date": "2024-01-28 12:00:00",
                "risk_level": "LOW",
            },
            {
                "number": "129",
                "title": "Payment integration work",
                "description": "Integrate new payment gateway",
                "status": "IN_PROGRESS",
                "priority": "HIGH",
                "category": "INTEGRATION",
                "assigned_to": "<EMAIL>",
                "created_date": "2024-01-08 11:00:00",
                "updated_date": "2024-01-22 14:00:00",
                "risk_level": "MEDIUM",
            },
        ]

        for cr in test_change_requests:
            cursor.execute(
                """
                INSERT INTO change_requests 
                (number, title, description, status, priority, category, assigned_to, 
                 created_date, updated_date, risk_level)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """,
                (
                    cr["number"],
                    cr["title"],
                    cr["description"],
                    cr["status"],
                    cr["priority"],
                    cr["category"],
                    cr["assigned_to"],
                    cr["created_date"],
                    cr["updated_date"],
                    cr["risk_level"],
                ),
            )

        conn.commit()
        conn.close()

        return self.test_db_path

    def create_change_request_service(self) -> ChangeRequestService:
        """Create a change request service with test database"""
        # Ensure test database is created
        if self.test_db_path is None:
            self.create_test_sqlite_database()

        assert self.test_db_path is not None, "Test database path should be set"

        sql_config = SqlConfig(
            enabled=True,
            driver="sqlite",
            database=self.test_db_path,
            change_request_query="""
                SELECT id, number, title, description, priority, status,
                       created_date, assigned_to, category, risk_level
                FROM change_requests 
                WHERE number = :change_request_number
            """,
            change_request_patterns=[
                r'BUG[#\-]\s*(\d+)',      # BUG# or BUG- followed by number
                r'BUG\s+(\d+):',          # BUG followed by space and number with colon
                r'Bug[#\-]\s*(\d+)',      # Bug# or Bug- followed by number
                r'Bug\s+(\d+):',          # Bug followed by space and number with colon
                r'bug[#\-]\s*(\d+)',      # bug# or bug- followed by number
                r'bug\s+(\d+):',          # bug followed by space and number with colon
                r'CR[#\-]\s*(\d+)',       # CR# or CR- followed by number
                r'CR\s+(\d+):',           # CR followed by space and number with colon
                r'CR\s+(\d+)',            # CR followed by space and number (no colon)
                r'Change[#\-]\s*(\d+)',   # Change# or Change- followed by number
                r'Change\s+Request\s+(\d+)',  # Change Request followed by number
                r'Request[#\-]\s*(\d+)',  # Request# or Request- followed by number
                r'Ticket[#\-]\s*(\d+)',   # Ticket# or Ticket- followed by number
                r'Ticket\s+(\d+):',       # Ticket followed by space and number with colon
                r'Issue[#\-]\s*(\d+)',    # Issue# or Issue- followed by number
                r'Issue\s+(\d+):',        # Issue followed by space and number with colon
                r'issue[#\-]\s*(\d+)',    # issue# or issue- followed by number
                r'issue\s+(\d+):',        # issue followed by space and number with colon
                r'#(\d+)',                # Hash followed by number
            ]
        )

        config = Config(sql_config=sql_config)
        return ChangeRequestService(config)

    def load_cr_patterns(self) -> List[str]:
        """Load change request patterns (from original test-connection.py)"""
        try:
            import json
            
            # Try to load from config file
            config_path = os.path.join(os.path.dirname(__file__), '..', 'data', 'config.json')
            if os.path.exists(config_path):
                with open(config_path, 'r') as f:
                    config = json.load(f)
                    if 'sql_config' in config and 'change_request_patterns' in config['sql_config']:
                        return config['sql_config']['change_request_patterns']

            # Fallback patterns
            return [
                r'BUG[#\-]\s*(\d+)',      # BUG# or BUG- followed by number
                r'BUG\s+(\d+):',          # BUG followed by space and number with colon
                r'Bug[#\-]\s*(\d+)',      # Bug# or Bug- followed by number
                r'Bug\s+(\d+):',          # Bug followed by space and number with colon
                r'bug[#\-]\s*(\d+)',      # bug# or bug- followed by number
                r'bug\s+(\d+):',          # bug followed by space and number with colon
                r'CR[#\-]\s*(\d+)',       # CR# or CR- followed by number
                r'CR\s+(\d+):',           # CR followed by space and number with colon
                r'CR\s+(\d+)',            # CR followed by space and number (no colon)
                r'Change[#\-]\s*(\d+)',   # Change# or Change- followed by number
                r'Change\s+Request\s+(\d+)',  # Change Request followed by number
                r'Request[#\-]\s*(\d+)',  # Request# or Request- followed by number
                r'Ticket[#\-]\s*(\d+)',   # Ticket# or Ticket- followed by number
                r'Ticket\s+(\d+):',       # Ticket followed by space and number with colon
                r'Issue[#\-]\s*(\d+)',    # Issue# or Issue- followed by number
                r'Issue\s+(\d+):',        # Issue followed by space and number with colon
                r'issue[#\-]\s*(\d+)',    # issue# or issue- followed by number
                r'issue\s+(\d+):',        # issue followed by space and number with colon
                r'#(\d+)',                # Hash followed by number
            ]

        except Exception as e:
            print(f"Error loading patterns from config: {e}")
            return [r'#(\d+)']  # Minimal pattern as last resort

    def extract_change_request_numbers(self, commit_message: str) -> List[str]:
        """Extract change request numbers from commit message using patterns"""
        patterns = self.load_cr_patterns()
        numbers = []
        for pattern in patterns:
            matches = re.findall(pattern, commit_message, re.IGNORECASE)
            numbers.extend(matches)
        return list(set(numbers))  # Remove duplicates

    @pytest.mark.unit
    def test_sqlite_database_connection(self):
        """Test basic SQLite database connection (from original test_database_connection)"""
        self.create_test_sqlite_database()

        # Test connection
        conn = sqlite3.connect(self.test_db_path)
        cursor = conn.cursor()

        # Test basic query
        cursor.execute("SELECT COUNT(*) FROM change_requests")
        count = cursor.fetchone()[0]
        self.assertEqual(count, 6)  # We inserted 6 test records

        # Test version query (SQLite equivalent)
        cursor.execute("SELECT sqlite_version()")
        version = cursor.fetchone()
        self.assertIsNotNone(version[0])

        conn.close()

    @pytest.mark.unit
    def test_sample_data_retrieval(self):
        """Test sample data retrieval (from original test_sample_data)"""
        self.create_test_sqlite_database()

        conn = sqlite3.connect(self.test_db_path)
        cursor = conn.cursor()

        # Count total change requests
        cursor.execute("SELECT COUNT(*) FROM change_requests")
        count = cursor.fetchone()[0]
        self.assertEqual(count, 6)

        # Show sample records
        cursor.execute("""
            SELECT number, title, priority, status, category
            FROM change_requests
            ORDER BY number
            LIMIT 5
        """)

        records = cursor.fetchall()
        self.assertEqual(len(records), 5)

        # Verify first record
        first_record = records[0]
        self.assertEqual(first_record[0], "123")  # number
        self.assertEqual(first_record[1], "Fix authentication bug")  # title
        self.assertEqual(first_record[2], "HIGH")  # priority
        self.assertEqual(first_record[3], "OPEN")  # status
        self.assertEqual(first_record[4], "BUG")  # category

        conn.close()

    @pytest.mark.unit
    def test_change_request_query(self):
        """Test change request lookup query (from original test_change_request_query)"""
        self.create_test_sqlite_database()
        service = self.create_change_request_service()

        # Test retrieval of existing change request
        cr_info = service.get_change_request_info("123")
        self.assertIsNotNone(cr_info)
        assert cr_info is not None  # Type assertion for type checker
        self.assertEqual(cr_info.number, "123")
        self.assertEqual(cr_info.title, "Fix authentication bug")
        self.assertEqual(cr_info.priority, "HIGH")
        self.assertEqual(cr_info.status, "OPEN")
        self.assertEqual(cr_info.category, "BUG")
        self.assertEqual(cr_info.assigned_to, "<EMAIL>")

        # Test retrieval of non-existent change request
        cr_info = service.get_change_request_info("999")
        self.assertIsNone(cr_info)

    @pytest.mark.unit
    def test_commit_message_parsing(self):
        """Test commit message parsing with various formats (from original test_commit_message_parsing)"""
        # Test with patterns that work with fallback (only hash pattern)
        test_messages = [
            ("Simple hash reference #123", ["123"]),
            ("Multiple hash references #125 and #127", ["125", "127"]),
            ("No change request mentioned in this commit", []),
            ("Hash at end #456", ["456"]),
        ]

        for message, expected_numbers in test_messages:
            with self.subTest(message=message):
                numbers = self.extract_change_request_numbers(message)
                # Sort both lists for comparison since order doesn't matter
                self.assertEqual(sorted(numbers), sorted(expected_numbers))

    @pytest.mark.unit
    def test_commit_message_parsing_with_service(self):
        """Test commit message parsing using the change request service patterns"""
        service = self.create_change_request_service()

        # Test messages that should work with service patterns
        test_messages = [
            ("Fix authentication bug - CR#123", ["123"]),
            ("Implement reporting feature for Change Request 124", ["124"]),
            ("Database optimization - addresses Issue #125", ["125"]),
            ("Security patch deployment - Ticket-127", ["127"]),
            ("UI improvements - resolves CR 128", ["128"]),
            ("Payment integration work - Change #129", ["129"]),
            ("Multiple issues: CR-123, Issue #125", ["123", "125"]),  # Simplified to avoid Ticket pattern issues
            ("No change request mentioned in this commit", []),
            ("Simple hash reference #456", ["456"]),
        ]

        for message, expected_numbers in test_messages:
            with self.subTest(message=message):
                numbers = service.extract_change_request_numbers(message)
                # Sort both lists for comparison since order doesn't matter
                self.assertEqual(sorted(numbers), sorted(expected_numbers))

    @pytest.mark.unit
    def test_change_request_service_integration(self):
        """Test change request service integration with database"""
        self.create_test_sqlite_database()
        service = self.create_change_request_service()

        # Test multiple change request retrieval
        cr_numbers = ["123", "124", "999"]  # Mix of existing and non-existing
        change_requests = service.get_multiple_change_requests(cr_numbers)

        # Should return 2 change requests (123 and 124 exist, 999 doesn't)
        self.assertEqual(len(change_requests), 2)

        # Verify the returned change requests
        cr_numbers_found = [cr.number for cr in change_requests]
        self.assertIn("123", cr_numbers_found)
        self.assertIn("124", cr_numbers_found)
        self.assertNotIn("999", cr_numbers_found)

    @pytest.mark.unit
    def test_pattern_loading_from_config(self):
        """Test loading patterns from configuration"""
        patterns = self.load_cr_patterns()

        # Should have at least the basic patterns
        self.assertGreater(len(patterns), 0)

        # Should include the CR pattern without colon (recent addition)
        self.assertIn(r'CR\s+(\d+)', patterns)

        # Should include basic hash pattern
        self.assertIn(r'#(\d+)', patterns)

    @pytest.mark.integration
    @pytest.mark.skipif(not MYSQL_AVAILABLE, reason="PyMySQL not available")
    def test_mysql_database_connection(self):
        """Test MySQL database connection (integration test)"""
        # MySQL configuration for test database
        db_config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'reposense',
            'password': 'reposense123',
            'database': 'change_requests',
            'charset': 'utf8mb4'
        }

        try:
            connection = pymysql.connect(**db_config)

            with connection.cursor() as cursor:
                cursor.execute("SELECT VERSION()")
                version = cursor.fetchone()
                self.assertIsNotNone(version[0])

            connection.close()

        except Exception as e:
            self.skipTest(f"MySQL test database not available: {e}")

    @pytest.mark.integration
    @pytest.mark.skipif(not MYSQL_AVAILABLE, reason="PyMySQL not available")
    def test_mysql_sample_data(self):
        """Test MySQL sample data retrieval (integration test)"""
        # MySQL configuration for test database
        db_config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'reposense',
            'password': 'reposense123',
            'database': 'change_requests',
            'charset': 'utf8mb4'
        }

        try:
            connection = pymysql.connect(**db_config)

            with connection.cursor() as cursor:
                # Count total change requests
                cursor.execute("SELECT COUNT(*) FROM change_requests")
                count = cursor.fetchone()[0]
                self.assertGreaterEqual(count, 0)  # Should have at least 0 records

                # Show sample records if any exist
                cursor.execute("""
                    SELECT number, title, priority, status, category
                    FROM change_requests
                    ORDER BY number
                    LIMIT 5
                """)

                records = cursor.fetchall()
                # Just verify we can query without errors
                self.assertIsInstance(records, tuple)

            connection.close()

        except Exception as e:
            self.skipTest(f"MySQL test database not available: {e}")


if __name__ == "__main__":
    unittest.main()
